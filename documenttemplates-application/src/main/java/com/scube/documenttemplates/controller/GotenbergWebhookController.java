package com.scube.documenttemplates.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.documenttemplates.permission.Permissions;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import com.scube.documenttemplates.service.FileService;
import com.scube.lib.misc.annotations.validation.NoValidation;
import com.scube.rabbit.core.AmqpGateway;

import jakarta.annotation.security.PermitAll;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/webhook/gotenberg")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.DOCUMENT_TEMPLATE_SERVICE)
@Validated
public class GotenbergWebhookController {
    private final FileService fileService;
    private final AmqpGateway amqpGateway;

    @PostMapping(consumes = MediaType.APPLICATION_PDF_VALUE)
    @PermitAll
    public ResponseEntity<String> handleWebhook(@RequestBody byte[] pdfBytes,
                                                @RequestHeader("Parent-Type") @Size(max = 255) String parentType,
                                                @RequestHeader("Parent-Id") @Size(max = 255) String parentId,
                                                @RequestHeader(value = "Group-Id", required = false) @Size(max = 255) String groupId) {
        try {
            log.info("Received webhook for Parent-Type: {}, Parent-Id: {}, Group-Id: {}", parentType, parentId, groupId);
            var contentType = fileService.determineContentType("pdf");
            var fileResponse = fileService.saveFilledTemplateFile(pdfBytes, UUID.randomUUID() + ".pdf", contentType);
            var documentId = fileResponse.getDocumentUUID();
            amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedEvent(parentId, parentType, groupId, documentId.toString()));
            log.info("Processed webhook and saved document with ID: {}", documentId);
            return ResponseEntity.ok("Webhook received");
        } catch (Exception e) {
            amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(parentId, parentType, groupId, e.getMessage()));
            throw e;
        }
    }

    @PostMapping("/error")
    @PermitAll
    public ResponseEntity<String> handleWebhookError(@RequestBody @NoValidation String payload,
                                                     @RequestHeader("Parent-Type") @Size(max = 255) String parentType,
                                                     @RequestHeader("Parent-Id") @Size(max = 255) String parentId,
                                                     @RequestHeader(value = "Group-Id", required = false) @Size(max = 255) String groupId) {
        log.info("Received webhook error for Parent-Type: {}, Parent-Id: {}, Group-Id: {}. Payload: {}", parentType, parentId, groupId, payload);
        amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(parentId, parentType, groupId, payload));
        log.info("Processed webhook error for Parent-Id: {}", parentId);
        return ResponseEntity.ok("Webhook received");
    }

    @GetMapping("/test")
    public ResponseEntity<String> testEndpoint() {
        log.info("Test endpoint called - no security");
        return ResponseEntity.ok("Test endpoint works");
    }
}